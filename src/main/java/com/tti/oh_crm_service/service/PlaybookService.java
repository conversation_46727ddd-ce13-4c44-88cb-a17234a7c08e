package com.tti.oh_crm_service.service;

import com.tti.oh_crm_service.entity.*;
import org.springframework.data.domain.Page;

import java.util.List;

public interface PlaybookService {

    // PlaybookTool CRUD operations
    Response<PlaybookToolListView> createPlaybookTool(CreatePlaybookToolRequest request, Long accountId);
    Response<PlaybookToolListView> updatePlaybookTool(UpdatePlaybookToolRequest request, Long accountId);
    Response<Page<PlaybookToolListView>> getPlaybookToolsList(int page, int limit, String search, String sortBy, String sortDirection, Long accountId);
    Response<PlaybookToolDetailView> getPlaybookToolDetail(Long id, Long accountId);
    Response<String> deletePlaybookTool(Long id, Long accountId);

    // PlaybookToolSection CRUD operations
    Response<PlaybookToolSectionView> createPlaybookToolSection(CreatePlaybookToolSectionRequest request, Long playbookToolId, Long accountId);
    Response<PlaybookToolSectionView> updatePlaybookToolSection(UpdatePlaybookToolSectionRequest request, Long playbookToolId, Long accountId);

    // Bulk PlaybookToolSection operations
    Response<PlaybookToolWithSectionsResponse> updatePlaybookToolWithSections(UpdatePlaybookToolWithSectionsRequest request, Long playbookToolId, Long accountId);

    // PlaybookToolHistory operations
    Response<Page<PlaybookToolHistoryView>> getPlaybookToolHistories(Long playbookToolId, int page, int limit, String sortBy, String sortDirection, Long accountId);
    Response<String> restorePlaybookToolSectionFromHistory(Long historyId, Long accountId);

    // GroupedModulePlaybookProcess operations
    Response<List<GroupedModulePlaybookProcessView>> getAllGroupedModulePlaybookProcesses(Long accountId);

    // ModuleStagePlaybook operations
    Response<List<ModuleStagePlaybookView>> getModuleStagePlaybooksByModuleCode(String moduleCode, Long accountId, String language);
    Response<ModuleStagePlaybookDetailView> getModuleStagePlaybookDetail(Long id, Long accountId);
    Response<ModuleStagePlaybookDetailView> updateModuleStagePlaybookGuideForSuccessContent(UpdateModuleStagePlaybookContentRequest request, Long accountId);
    Response<Page<ModuleStagePlaybookToolView>> getModuleStagePlaybookTools(Long moduleStagePlaybookId, int page, int limit, String sortBy, String sortDirection, Long accountId);
    Response<String> addPlaybookToolsToModuleStagePlaybook(AddPlaybookToolsRequest request, Long accountId);
    Response<String> removePlaybookToolsFromModuleStagePlaybook(RemovePlaybookToolsRequest request, Long accountId);
}
